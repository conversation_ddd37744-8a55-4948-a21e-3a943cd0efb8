import { prisma } from "@/lib/database/prisma";

// Default life aspects that should be created for new users
const DEFAULT_LIFE_ASPECTS = [
  {
    name: "<PERSON>",
    color: "#9b59b6",
    sort_order: 0,
    description: "Mental health, learning, and personal development",
  },
  {
    name: "Body",
    color: "#e74c3c",
    sort_order: 1,
    description: "Physical health, fitness, and wellness",
  },
  {
    name: "Career",
    color: "#3498db",
    sort_order: 2,
    description: "Professional development and work-related goals",
  },
  {
    name: "Finance",
    color: "#2ecc71",
    sort_order: 3,
    description: "Financial planning, investments, and money management",
  },
  {
    name: "Relationships",
    color: "#f39c12",
    sort_order: 4,
    description: "Family, friends, and social connections",
  },
  {
    name: "Recreation",
    color: "#1abc9c",
    sort_order: 5,
    description: "Hobbies, entertainment, and leisure activities",
  },
  {
    name: "Environment",
    color: "#34495e",
    sort_order: 6,
    description: "Home, workspace, and living environment",
  },
];

// Default custom fields that should be created for new users
const DEFAULT_CUSTOM_FIELDS = [
  {
    name: "Priority",
    field_type: "SINGLE_SELECT",
    is_required: false,
    sort_order: 0,
    choice_options: [
      { value: "Urgent - Uplift", color: "#d32f2f", sort_order: 0, is_default: false },
      { value: "Urgent - Not Uplift", color: "#ed6c02", sort_order: 1, is_default: false },
      { value: "Not Urgent - Uplift", color: "#1976d2", sort_order: 2, is_default: false },
      { value: "Not Urgent - Not Uplift", color: "#757575", sort_order: 3, is_default: false },
    ],
  },
  {
    name: "Status",
    field_type: "SINGLE_SELECT",
    is_required: false,
    sort_order: 1,
    choice_options: [
      { value: "Not Started", color: "#757575", sort_order: 0, is_default: true },
      { value: "In Progress", color: "#0288d1", sort_order: 1, is_default: false },
      { value: "Done", color: "#2e7d32", sort_order: 2, is_default: false },
      { value: "In Future", color: "#9e9e9e", sort_order: 3, is_default: false },
    ],
  },
  {
    name: "Start Date",
    field_type: "DATE",
    is_required: false,
    sort_order: 2,
  },
  {
    name: "End Date",
    field_type: "DATE",
    is_required: false,
    sort_order: 3,
  },
  {
    name: "Planned Week",
    field_type: "WEEK",
    is_required: false,
    sort_order: 4,
  },
];

/**
 * Creates default life aspects for a new user
 * @param userId - The ID of the user to create life aspects for
 * @returns Promise<void>
 */
export async function createDefaultLifeAspects(userId: string): Promise<void> {
  try {
    console.log("Creating default life aspects for user:", userId);

    // Check if user already has life aspects
    const existingCount = await prisma.workitems_life_aspect.count({
      where: { user_id: userId },
    });

    if (existingCount > 0) {
      console.log("User already has life aspects, skipping creation");
      return;
    }

    // Create default life aspects
    const lifeAspectsData = DEFAULT_LIFE_ASPECTS.map((aspect) => ({
      user_id: userId,
      name: aspect.name,
      color: aspect.color,
      sort_order: aspect.sort_order,
    }));

    await prisma.workitems_life_aspect.createMany({
      data: lifeAspectsData,
    });

    console.log(`Created ${DEFAULT_LIFE_ASPECTS.length} default life aspects for user:`, userId);
  } catch (error) {
    console.error("Error creating default life aspects:", error);
    throw error;
  }
}

/**
 * Creates default user preferences
 * @param userId - The ID of the user to create preferences for
 * @returns Promise<void>
 */
export async function createDefaultUserPreferences(userId: string): Promise<void> {
  try {
    console.log("Creating default user preferences for user:", userId);

    // Check if user already has preferences
    const existingPreferences = await prisma.auth_user_preferences.findUnique({
      where: { user_id: userId },
    });

    if (existingPreferences) {
      console.log("User already has preferences, skipping creation");
      return;
    }

    // Default preferences structure
    const defaultPreferences = {
      // Custom field display preferences
      custom_field_preferences: {},

      // Table display preferences
      table_preferences: {
        show_completed_outcomes: true,
        show_sub_projects: true,
        compact_view: false,
        sort_by: "created_at",
        sort_order: "asc",
      },

      // Dashboard preferences
      dashboard_preferences: {
        show_recent_projects: true,
        show_upcoming_deadlines: true,
        show_progress_charts: true,
        default_time_range: "30_days",
      },

      // Notification preferences
      notification_preferences: {
        email_notifications: true,
        deadline_reminders: true,
        project_updates: true,
      },
    };

    await prisma.auth_user_preferences.create({
      data: {
        user_id: userId,
        preferences: defaultPreferences,
      },
    });

    console.log("Created default user preferences for user:", userId);
  } catch (error) {
    console.error("Error creating default user preferences:", error);
    throw error;
  }
}

/**
 * Creates default custom fields for a new user
 * @param userId - The ID of the user to create custom fields for
 * @returns Promise<void>
 */
export async function createDefaultCustomFields(userId: string): Promise<void> {
  try {
    console.log("Creating default custom fields for user:", userId);

    // Use a transaction to ensure all fields are created successfully or none are
    await prisma.$transaction(async (tx) => {
      for (const fieldData of DEFAULT_CUSTOM_FIELDS) {
        // Check if a field with this name already exists for the user (idempotency)
        const existingField = await tx.workitems_custom_field_definition.findFirst({
          where: {
            user_id: userId,
            name: fieldData.name,
          },
        });

        if (existingField) {
          console.log(`Field "${fieldData.name}" already exists for user ${userId}, skipping`);
          continue;
        }

        // Create the custom field definition
        const fieldDefinition = await tx.workitems_custom_field_definition.create({
          data: {
            user_id: userId,
            name: fieldData.name,
            field_type: fieldData.field_type,
            is_required: fieldData.is_required,
            sort_order: fieldData.sort_order,
          },
        });

        // Create the choice options for this field
        if (fieldData.choice_options && fieldData.choice_options.length > 0) {
          await tx.workitems_custom_field_choice_option.createMany({
            data: fieldData.choice_options.map((option) => ({
              field_definition_id: fieldDefinition.id,
              value: option.value,
              color: option.color,
              sort_order: option.sort_order,
              is_default: option.is_default,
            })),
          });
        }

        console.log(`Created custom field "${fieldData.name}" with ${fieldData.choice_options?.length || 0} choice options for user:`, userId);
      }
    });

    console.log(`Successfully created ${DEFAULT_CUSTOM_FIELDS.length} default custom fields for user:`, userId);
  } catch (error) {
    console.error("Error creating default custom fields:", error);
    throw error;
  }
}

/**
 * Sets up all default data for a new user
 * @param userId - The ID of the user to set up default data for
 * @returns Promise<void>
 */
export async function setupDefaultUserData(userId: string): Promise<void> {
  try {
    console.log("Setting up default data for new user:", userId);

    // Create default life aspects, preferences, and custom fields in parallel
    await Promise.all([createDefaultLifeAspects(userId), createDefaultUserPreferences(userId), createDefaultCustomFields(userId)]);

    console.log("Successfully set up default data for user:", userId);
  } catch (error) {
    console.error("Error setting up default user data:", error);
    throw error;
  }
}
