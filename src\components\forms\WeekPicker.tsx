"use client";

import React, { useState, useEffect, useRef } from "react";
import { TextField, Popover, Box, Typography, IconButton, Grid, Paper, useTheme } from "@mui/material";
import { ChevronLeft, ChevronRight, Clear as ClearIcon } from "@mui/icons-material";
import {
  format,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  getWeek,
  addMonths,
  subMonths,
  isSameWeek,
  isToday,
  parse,
  isValid,
} from "date-fns";
import { userPreferencesService } from "@/lib/api/userPreferencesService";

interface WeekPickerProps {
  label?: string;
  value?: string | null; // YYYY-WNN format
  onChange: (value: string | null) => void;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  fullWidth?: boolean;
  size?: "small" | "medium";
  sx?: any;
}

const WeekPicker: React.FC<WeekPickerProps> = ({
  label,
  value,
  onChange,
  disabled = false,
  required = false,
  error = false,
  helperText,
  fullWidth = true,
  size = "small",
  sx,
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [weekStartsOn, setWeekStartsOn] = useState<0 | 1>(0); // 0 = Sunday, 1 = Monday
  const [hoveredWeek, setHoveredWeek] = useState<Date | null>(null);
  const textFieldRef = useRef<HTMLInputElement>(null);

  // Fetch user preference for week start day
  useEffect(() => {
    const fetchWeekStartsOn = async () => {
      try {
        const preferences = await userPreferencesService.getPreferences();
        setWeekStartsOn(preferences.week_starts_on === "Monday" ? 1 : 0);
      } catch (error) {
        console.warn("Failed to fetch week starts on preference:", error);
        // Default to Sunday if fetch fails
        setWeekStartsOn(0);
      }
    };

    fetchWeekStartsOn();
  }, []);

  // Parse value to get selected week
  const selectedWeek = React.useMemo(() => {
    if (!value) return null;

    // Parse YYYY-WNN format
    const match = value.match(/^(\d{4})-W(\d{2})$/);
    if (!match) return null;

    const year = parseInt(match[1]);
    const weekNumber = parseInt(match[2]);

    // Create a date for the first day of the year
    const firstDayOfYear = new Date(year, 0, 1);

    // Find the first week of the year
    const firstWeek = startOfWeek(firstDayOfYear, { weekStartsOn });

    // Calculate the target week
    const targetWeek = new Date(firstWeek.getTime() + (weekNumber - 1) * 7 * 24 * 60 * 60 * 1000);

    return targetWeek;
  }, [value, weekStartsOn]);

  // Format selected week for display
  const displayValue = React.useMemo(() => {
    if (!selectedWeek) return "";

    const weekStart = startOfWeek(selectedWeek, { weekStartsOn });
    const weekEnd = endOfWeek(selectedWeek, { weekStartsOn });
    const weekNumber = getWeek(selectedWeek, { weekStartsOn });

    return `W${weekNumber}: ${format(weekStart, "d MMM")} - ${format(weekEnd, "d MMM")}`;
  }, [selectedWeek, weekStartsOn]);

  const handleTextFieldClick = (event: React.MouseEvent<HTMLElement>) => {
    if (!disabled) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
    setHoveredWeek(null);
  };

  const handleClear = (event: React.MouseEvent) => {
    event.stopPropagation();
    onChange(null);
  };

  const handlePreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const handleWeekSelect = (date: Date) => {
    const weekNumber = getWeek(date, { weekStartsOn });
    const year = date.getFullYear();
    const weekValue = `${year}-W${weekNumber.toString().padStart(2, "0")}`;
    onChange(weekValue);
    handleClose();
  };

  const handleDayHover = (date: Date) => {
    setHoveredWeek(date);
  };

  const handleDayLeave = () => {
    setHoveredWeek(null);
  };

  // Generate calendar days
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const calendarStart = startOfWeek(monthStart, { weekStartsOn });
  const calendarEnd = endOfWeek(monthEnd, { weekStartsOn });

  const calendarDays = eachDayOfInterval({
    start: calendarStart,
    end: calendarEnd,
  });

  // Group days by weeks
  const weeks: Date[][] = [];
  for (let i = 0; i < calendarDays.length; i += 7) {
    weeks.push(calendarDays.slice(i, i + 7));
  }

  const open = Boolean(anchorEl);

  return (
    <>
      <TextField
        ref={textFieldRef}
        label={label}
        value={displayValue}
        onClick={handleTextFieldClick}
        disabled={disabled}
        required={required}
        error={error}
        helperText={helperText}
        fullWidth={fullWidth}
        size={size}
        sx={sx}
        placeholder="W25: 15 Jun - 21 Jun"
        InputProps={{
          readOnly: true,
          endAdornment:
            value && !disabled ? (
              <IconButton size="small" onClick={handleClear} edge="end" sx={{ mr: -0.5 }}>
                <ClearIcon fontSize="small" />
              </IconButton>
            ) : null,
        }}
      />

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        <Paper sx={{ p: 2, minWidth: 320 }}>
          {/* Month Navigation */}
          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 2 }}>
            <IconButton size="small" onClick={handlePreviousMonth}>
              <ChevronLeft />
            </IconButton>
            <Typography variant="h6" sx={{ fontWeight: 500 }}>
              {format(currentMonth, "MMMM yyyy")}
            </Typography>
            <IconButton size="small" onClick={handleNextMonth}>
              <ChevronRight />
            </IconButton>
          </Box>

          {/* Day Headers */}
          <Grid container spacing={0} sx={{ mb: 1 }}>
            <Grid item xs={1.5}>
              <Typography
                variant="caption"
                sx={{
                  display: "block",
                  textAlign: "center",
                  fontWeight: 500,
                  color: theme.palette.text.disabled,
                  fontSize: "0.7rem",
                }}
              >
                W
              </Typography>
            </Grid>
            {["S", "M", "T", "W", "T", "F", "S"].map((day, index) => {
              // Adjust day headers based on week start preference
              const adjustedIndex = weekStartsOn === 1 ? (index + 1) % 7 : index;
              const dayHeaders = ["S", "M", "T", "W", "T", "F", "S"];
              return (
                <Grid item xs={1.5} key={index}>
                  <Typography
                    variant="caption"
                    sx={{
                      display: "block",
                      textAlign: "center",
                      fontWeight: 500,
                      color: theme.palette.text.secondary,
                    }}
                  >
                    {dayHeaders[adjustedIndex]}
                  </Typography>
                </Grid>
              );
            })}
          </Grid>

          {/* Calendar Grid */}
          {weeks.map((week, weekIndex) => {
            const weekStart = week[0];
            const weekNumber = getWeek(weekStart, { weekStartsOn });
            const isCurrentWeek = week.some((day) => isToday(day));
            const isSelectedWeek = selectedWeek && isSameWeek(weekStart, selectedWeek, { weekStartsOn });
            const isHoveredWeek = hoveredWeek && isSameWeek(weekStart, hoveredWeek, { weekStartsOn });

            return (
              <Grid container spacing={0} key={weekIndex}>
                {/* Week Number */}
                <Grid item xs={1.5}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      height: 32,
                      fontSize: "0.7rem",
                      color: theme.palette.text.disabled,
                      fontWeight: 400,
                    }}
                  >
                    {weekNumber}
                  </Box>
                </Grid>

                {/* Week Days */}
                {week.map((day, dayIndex) => {
                  const isOutsideMonth = day.getMonth() !== currentMonth.getMonth();

                  return (
                    <Grid item xs={1.5} key={dayIndex}>
                      <Box
                        onClick={() => handleWeekSelect(day)}
                        onMouseEnter={() => handleDayHover(day)}
                        onMouseLeave={handleDayLeave}
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          height: 32,
                          cursor: "pointer",
                          borderRadius: 1,
                          fontSize: "0.875rem",
                          color: isOutsideMonth ? theme.palette.text.disabled : theme.palette.text.primary,
                          backgroundColor: isCurrentWeek
                            ? theme.palette.action.selected
                            : isSelectedWeek
                            ? theme.palette.primary.main
                            : isHoveredWeek
                            ? theme.palette.action.hover
                            : "transparent",
                          color: isSelectedWeek
                            ? theme.palette.primary.contrastText
                            : isOutsideMonth
                            ? theme.palette.text.disabled
                            : theme.palette.text.primary,
                          "&:hover": {
                            backgroundColor: isSelectedWeek ? theme.palette.primary.dark : theme.palette.action.hover,
                          },
                        }}
                      >
                        {format(day, "d")}
                      </Box>
                    </Grid>
                  );
                })}
              </Grid>
            );
          })}
        </Paper>
      </Popover>
    </>
  );
};

export default WeekPicker;
